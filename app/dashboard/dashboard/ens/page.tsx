'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectOption } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount, useChainId, useSwitchChain } from 'wagmi';
import { mainnet } from 'wagmi/chains';
import { ethers } from 'ethers';
import {
  getENSOwner,
  transferENSOwnership,
  getENSAvatar,
  isValidENSName,
  shortenAddress
} from "@/lib/ens-utils";
import {
  WalletIcon,
  CheckCircleIcon,
  ExternalLinkIcon,
  CopyIcon,
  RefreshCwIcon,
  HelpCircleIcon,
  ArrowRightIcon,
  InfoIcon,
  GlobeIcon,
  LinkIcon,
  UserCheckIcon,
  ImageIcon
} from "lucide-react";

import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";
import { DashboardLayoutWrapper } from "../layout";

interface ENSConnection {
  projectId: string;
  ensName: string;
  owner: string;
  avatar?: string;
  connectedAt: string;
}

interface Application {
  id: string;
  name: string;
  description: string;
}

export default function ENSIntegrationPage() {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { switchChain } = useSwitchChain();
  const { showToast } = useToast();
  const { token } = useAuth();
  
  const [selectedProjectId, setSelectedProjectId] = useState('');
  const [ensName, setEnsName] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isTransferring, setIsTransferring] = useState(false);
  const [newOwnerAddress, setNewOwnerAddress] = useState('');
  const [ensConnections, setEnsConnections] = useState<ENSConnection[]>([]);
  const [currentENSData, setCurrentENSData] = useState<{
    owner: string | null;
    avatar: string | null;
    isOwned: boolean;
  } | null>(null);
  const [showTransferComponent, setShowTransferComponent] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const [signer, setSigner] = useState<ethers.JsonRpcSigner | null>(null);
  const [applications, setApplications] = useState<Application[]>([]);
  const [isLoadingApplications, setIsLoadingApplications] = useState(false);

  // Initialize provider and signer when wallet is connected
  useEffect(() => {
    const initializeProvider = async () => {
      if (isConnected && typeof window !== 'undefined' && window.ethereum) {
        try {
          const browserProvider = new ethers.BrowserProvider(window.ethereum);
          setProvider(browserProvider);

          const signer = await browserProvider.getSigner();
          setSigner(signer);
        } catch (error) {
          console.error('Failed to initialize provider:', error);
        }
      } else {
        setProvider(null);
        setSigner(null);
      }
    };

    initializeProvider();
  }, [isConnected]);

  // Fetch applications from API
  const fetchApplications = async () => {
    if (!token) return;

    setIsLoadingApplications(true);
    try {
      const response = await apiService.getApplications(token);
      const apps = response.map((app: any) => ({
        id: app.id,
        name: app.name,
        description: app.description || 'No description available'
      }));
      setApplications(apps);
    } catch (error) {
      console.error('Failed to fetch applications:', error);
      showToast({
        type: 'error',
        title: 'Failed to Load Applications',
        description: 'Could not load your applications. Please try again.'
      });
    } finally {
      setIsLoadingApplications(false);
    }
  };

  // Load applications on component mount
  useEffect(() => {
    fetchApplications();
  }, [token]);

  // Load existing ENS connections (mock data - replace with API call)
  useEffect(() => {
    const mockConnections: ENSConnection[] = [
      {
        projectId: '1',
        ensName: 'defiprotocol.eth',
        owner: '******************************************',
        avatar: '',
        connectedAt: '2024-01-15T10:30:00Z'
      }
    ];
    setEnsConnections(mockConnections);
  }, []);



  // Update step progression based on wallet connection
  useEffect(() => {
    if (isConnected && currentStep === 1) {
      setCurrentStep(2);
    } else if (!isConnected) {
      setCurrentStep(1);
      setSelectedProjectId('');
      setEnsName('');
      setCurrentENSData(null);
    }
  }, [isConnected, currentStep]);

  // Handle project selection
  const handleProjectSelection = (projectId: string) => {
    setSelectedProjectId(projectId);
    if (projectId && currentStep === 2) {
      setCurrentStep(3); // Move to step 3 after project selection
    }
  };

  // Handle ENS name input
  const handleENSNameChange = (name: string) => {
    setEnsName(name.toLowerCase());
    if (name && currentStep === 3) {
      setCurrentStep(4); // Move to step 4 after ENS name entry
    }
  };



  const handleSwitchNetwork = async () => {
    try {
      switchChain({ chainId: mainnet.id });
      showToast({
        type: 'success',
        title: 'Network Switched',
        description: 'Successfully switched to Ethereum Mainnet'
      });
    } catch {
      showToast({
        type: 'error',
        title: 'Network Switch Failed',
        description: 'Failed to switch to Ethereum Mainnet'
      });
    }
  };

  const handleVerifyENS = async () => {
    if (!provider || !address || !ensName || !selectedProjectId) {
      showToast({
        type: 'error',
        title: 'Missing Information',
        description: 'Please connect wallet, select project, and enter ENS name'
      });
      return;
    }

    if (!isValidENSName(ensName)) {
      showToast({
        type: 'error',
        title: 'Invalid ENS Name',
        description: 'Please enter a valid .eth domain name'
      });
      return;
    }

    setIsVerifying(true);
    
    try {
      const owner = await getENSOwner(ensName, provider);
      const avatar = await getENSAvatar(ensName, provider);
      
      if (!owner) {
        setCurrentENSData({ owner: null, avatar: null, isOwned: false });
        showToast({
          type: 'error',
          title: 'ENS Not Found',
          description: 'This ENS name does not exist or has no owner'
        });
        return;
      }

      const isOwned = owner.toLowerCase() === address.toLowerCase();
      setCurrentENSData({ owner, avatar, isOwned });

      if (isOwned) {
        // Add to connections
        const newConnection: ENSConnection = {
          projectId: selectedProjectId,
          ensName,
          owner: address,
          avatar: avatar || undefined,
          connectedAt: new Date().toISOString()
        };
        
        setEnsConnections(prev => {
          const filtered = prev.filter(conn => 
            !(conn.projectId === selectedProjectId && conn.ensName === ensName)
          );
          return [...filtered, newConnection];
        });

        showToast({
          type: 'success',
          title: 'ENS Verified & Connected',
          description: `Successfully connected ${ensName} to your project`
        });
        
        // Reset form
        setEnsName('');
        setSelectedProjectId('');
      } else {
        showToast({
          type: 'error',
          title: 'Ownership Verification Failed',
          description: `You don&apos;t own ${ensName}. Current owner: ${shortenAddress(owner)}`
        });
      }
    } catch (error) {
      console.error('ENS verification error:', error);
      showToast({
        type: 'error',
        title: 'Verification Failed',
        description: 'Failed to verify ENS ownership. Please try again.'
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleTransferOwnership = async () => {
    if (!signer || !ensName || !newOwnerAddress) {
      showToast({
        type: 'error',
        title: 'Missing Information',
        description: 'Please ensure all fields are filled'
      });
      return;
    }

    setIsTransferring(true);
    
    try {
      const tx = await transferENSOwnership(ensName, newOwnerAddress, signer);
      
      showToast({
        type: 'info',
        title: 'Transaction Submitted',
        description: 'Transfer transaction submitted. Waiting for confirmation...'
      });

      await tx.wait();
      
      showToast({
        type: 'success',
        title: 'Ownership Transferred',
        description: `Successfully transferred ${ensName} to ${shortenAddress(newOwnerAddress)}`
      });
      
      // Remove from connections since we no longer own it
      setEnsConnections(prev => 
        prev.filter(conn => conn.ensName !== ensName)
      );
      
      setNewOwnerAddress('');
      setCurrentENSData(null);
    } catch (error) {
      console.error('Transfer error:', error);
      showToast({
        type: 'error',
        title: 'Transfer Failed',
        description: 'Failed to transfer ENS ownership. Transaction was rejected or failed.'
      });
    } finally {
      setIsTransferring(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    showToast({
      type: 'success',
      title: 'Copied',
      description: 'Address copied to clipboard'
    });
  };

  const getProjectName = (projectId: string) => {
    return applications.find(p => p.id === projectId)?.name || 'Unknown Project';
  };

  return (
    <DashboardLayoutWrapper title="ENS Integration">
      <div className="flex gap-6 lg:gap-8">
        {/* Fixed Instructions Sidebar */}
        <aside className="w-80 bg-white/90 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl shadow-lg p-6 overflow-y-auto max-h-[calc(100vh-200px)] sticky top-0">
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-4 flex items-center gap-2">
                <InfoIcon className="h-5 w-5 text-[#4A148C]" />
                ENS Guide
              </h2>
            </div>

            {/* What is ENS */}
            <Card className="bg-gradient-to-br from-[#4A148C]/5 to-[#7B1FA2]/5 border-[#B497D6]/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-[#4A148C] flex items-center gap-2 font-semibold">
                  <InfoIcon className="h-4 w-4" />
                  What is ENS?
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-[#4A148C]/80 mb-3 leading-relaxed">
                  The Ethereum Name Service (ENS) is a distributed naming system that maps
                  human-readable names like &apos;alice.eth&apos; to wallet addresses and other resources.
                </p>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] text-xs border border-[#B497D6]/30">
                    Decentralized
                  </Badge>
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] text-xs border border-[#B497D6]/30">
                    Secure
                  </Badge>
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] text-xs border border-[#B497D6]/30">
                    Permanent
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* How to Get ENS */}
            <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2 text-[#4A148C] font-semibold">
                  <GlobeIcon className="h-4 w-4 text-green-600" />
                  How to Get ENS
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                <div className="space-y-2">
                  <div className="flex gap-2 text-xs">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">1</div>
                    <div>
                      <p className="font-medium text-[#4A148C]">Visit app.ens.domains</p>
                      <p className="text-gray-600">Search for available names</p>
                    </div>
                  </div>
                  <div className="flex gap-2 text-xs">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">2</div>
                    <div>
                      <p className="font-medium text-[#4A148C]">Register & Pay</p>
                      <p className="text-gray-600">Complete registration with ETH</p>
                    </div>
                  </div>
                  <div className="flex gap-2 text-xs">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">3</div>
                    <div>
                      <p className="font-medium text-[#4A148C]">Set Primary Name</p>
                      <p className="text-gray-600">Configure in your wallet</p>
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full text-xs border-[#7B1FA2]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 hover:text-[#4A148C] transition-all duration-300"
                  onClick={() => window.open('https://app.ens.domains', '_blank')}
                >
                  <ExternalLinkIcon className="mr-1 h-3 w-3" />
                  Get Your ENS Name
                </Button>
              </CardContent>
            </Card>

            {/* Integration Steps */}
            <Card className="bg-gradient-to-br from-[#7B1FA2]/10 to-[#4A148C]/5 border-[#B497D6]/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-[#4A148C] flex items-center gap-2 font-semibold">
                  <LinkIcon className="h-4 w-4" />
                  Integration Steps
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2 text-xs">
                  <div className="flex items-center gap-2 p-2 bg-white/60 backdrop-blur-sm rounded-lg border border-[#B497D6]/20">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">1</div>
                    <span className="text-[#4A148C] font-medium ml-2">Connect Wallet</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-white/60 backdrop-blur-sm rounded-lg border border-[#B497D6]/20">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">2</div>
                    <span className="text-[#4A148C] font-medium ml-2">Choose ENS Name</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-white/60 backdrop-blur-sm rounded-lg border border-[#B497D6]/20">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">3</div>
                    <span className="text-[#4A148C] font-medium ml-2">Register ENS Name</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2 text-[#4A148C] font-semibold">
                  <CheckCircleIcon className="h-4 w-4 text-green-600" />
                  Benefits
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="space-y-1 text-xs">
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Human-readable addresses</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Web3 identity & reputation</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Cross-platform compatibility</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Decentralized ownership</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

          </div>
        </aside>

        {/* Main Scrollable Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="space-y-6">
            {/* Connection Form - Step by Step */}
            <Card className="p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
              <h2 className="text-xl font-semibold mb-6 flex items-center gap-2 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                <WalletIcon className="h-5 w-5 text-[#4A148C]" />
                Connect ENS Name
              </h2>

              <div className="space-y-6">
                {/* Step 1: Wallet Connection */}
                <div className={`space-y-3 p-4 bg-[#B497D6]/5 rounded-xl border border-[#B497D6]/20 ${currentStep >= 1 ? 'opacity-100' : 'opacity-50'} transition-all duration-300`}>
                  <div className="flex items-center mb-4">
                    <div className="w-6 h-6 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">1</div>
                    <span className="text-[#4A148C] font-semibold ml-3">Connect Wallet</span>
                  </div>
                  
                  <div className="w-full">
                    <ConnectButton.Custom>
                      {({
                        account,
                        chain,
                        openAccountModal,
                        openChainModal,
                        openConnectModal,
                        authenticationStatus,
                        mounted,
                      }) => {
                        // Note: If your app doesn't use authentication, you
                        // can remove all 'authenticationStatus' checks
                        const ready = mounted && authenticationStatus !== 'loading';
                        const connected =
                          ready &&
                          account &&
                          chain &&
                          (!authenticationStatus ||
                            authenticationStatus === 'authenticated');

                        return (
                          <div
                            {...(!ready && {
                              'aria-hidden': true,
                              'style': {
                                opacity: 0,
                                pointerEvents: 'none',
                                userSelect: 'none',
                              },
                            })}
                          >
                            {(() => {
                              if (!connected) {
                                return (
                                  <Button
                                    onClick={openConnectModal}
                                    className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300"
                                  >
                                    Connect Wallet
                                  </Button>
                                );
                              }

                              if (chain.unsupported) {
                                return (
                                  <Button
                                    onClick={openChainModal}
                                    className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white hover:shadow-lg hover:shadow-red-500/25 hover:scale-105 transition-all duration-300"
                                  >
                                    Wrong network
                                  </Button>
                                );
                              }

                              return (
                                <div className="flex gap-2">
                                  <Button
                                    onClick={openChainModal}
                                    variant="outline"
                                    className="flex-1"
                                  >
                                    {chain.hasIcon && (
                                      <div
                                        style={{
                                          background: chain.iconBackground,
                                          width: 12,
                                          height: 12,
                                          borderRadius: 999,
                                          overflow: 'hidden',
                                          marginRight: 4,
                                        }}
                                      >
                                        {chain.iconUrl && (
                                          <img
                                            alt={chain.name ?? 'Chain icon'}
                                            src={chain.iconUrl}
                                            style={{ width: 12, height: 12 }}
                                          />
                                        )}
                                      </div>
                                    )}
                                    {chain.name}
                                  </Button>

                                  <Button
                                    onClick={openAccountModal}
                                    variant="outline"
                                    className="flex-1"
                                  >
                                    {account.displayName}
                                    {account.displayBalance
                                      ? ` (${account.displayBalance})`
                                      : ''}
                                  </Button>
                                </div>
                              );
                            })()}
                          </div>
                        );
                      }}
                    </ConnectButton.Custom>
                  </div>

                  {isConnected && chainId !== mainnet.id && (
                    <div className="p-3 bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg backdrop-blur-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-yellow-800 font-medium">
                          Switch to Ethereum Mainnet for ENS
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleSwitchNetwork}
                          className="border-yellow-300 hover:bg-yellow-100 hover:border-yellow-400 transition-all duration-300"
                        >
                          Switch Network
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Step 2: Project Selection */}
                {isConnected && (
                <div className={`space-y-3 p-4 bg-[#B497D6]/5 rounded-xl border border-[#B497D6]/20 ${currentStep >= 2 ? 'opacity-100' : 'opacity-50'} transition-all duration-300`}>
                  <div className="flex items-center gap-2 mb-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium shadow-lg ${
                      selectedProjectId ? 'bg-gradient-to-r from-green-500 to-green-600 text-white' : currentStep === 2 ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white' : 'bg-gray-100 text-gray-400'
                    }`}>
                      {selectedProjectId ? <CheckCircleIcon className="h-4 w-4" /> : '2'}
                    </div>
                    <Label htmlFor="project" className="text-base font-semibold text-[#4A148C]">Step 2: Select Project</Label>
                  </div>
                  
                  <Select
                    id="project"
                    value={selectedProjectId}
                    onChange={(e) => handleProjectSelection(e.target.value)}
                    disabled={currentStep < 2 || isLoadingApplications}
                  >
                    <SelectOption value="">
                      {isLoadingApplications ? 'Loading applications...' : 'Choose a project...'}
                    </SelectOption>
                    {applications.map(project => (
                      <SelectOption key={project.id} value={project.id}>
                        {project.name}
                      </SelectOption>
                    ))}
                  </Select>
                </div>
                )}

                {/* Step 3: ENS Name Input */}
                {isConnected && selectedProjectId && (
                <div className={`space-y-3 p-4 bg-[#B497D6]/5 rounded-xl border border-[#B497D6]/20 ${currentStep >= 3 ? 'opacity-100' : 'opacity-50'} transition-all duration-300`}>
                  <div className="flex items-center gap-2 mb-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium shadow-lg ${
                      ensName ? 'bg-gradient-to-r from-green-500 to-green-600 text-white' : currentStep === 3 ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white' : 'bg-gray-100 text-gray-400'
                    }`}>
                      {ensName ? <CheckCircleIcon className="h-4 w-4" /> : '3'}
                    </div>
                    <Label htmlFor="ensName" className="text-base font-semibold text-[#4A148C]">Step 3: Enter ENS Name</Label>
                  </div>
                  
                  <div className="relative">
                    <Input
                      id="ensName"
                      type="text"
                      placeholder="yourname.eth"
                      value={ensName}
                      onChange={(e) => handleENSNameChange(e.target.value)}
                      disabled={currentStep < 3}
                      className="pr-12"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-400">
                      .eth
                    </div>
                  </div>
                </div>
                )}

                {/* Step 4: Verify & Connect */}
                {isConnected && selectedProjectId && ensName && (
                <div className={`space-y-3 p-4 bg-[#B497D6]/5 rounded-xl border border-[#B497D6]/20 ${currentStep >= 4 ? 'opacity-100' : 'opacity-50'} transition-all duration-300`}>
                  <div className="flex items-center gap-2 mb-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium shadow-lg ${
                      currentENSData?.isOwned ? 'bg-gradient-to-r from-green-500 to-green-600 text-white' : currentStep === 4 ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white' : 'bg-gray-100 text-gray-400'
                    }`}>
                      {currentENSData?.isOwned ? <CheckCircleIcon className="h-4 w-4" /> : '4'}
                    </div>
                    <Label className="text-base font-semibold text-[#4A148C]">Step 4: Verify & Connect ENS</Label>
                  </div>

                  <Button
                    onClick={handleVerifyENS}
                    disabled={currentStep < 4 || !isConnected || !selectedProjectId || !ensName || isVerifying || chainId !== mainnet.id}
                    className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300"
                  >
                    {isVerifying ? (
                      <>
                        <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                        Verifying Ownership...
                      </>
                    ) : (
                      <>
                        <UserCheckIcon className="mr-2 h-4 w-4" />
                        Verify & Connect ENS
                      </>
                    )}
                  </Button>
                </div>
                )}

                {/* Current ENS Data Display */}
                {currentENSData && (
                  <div className="p-4 bg-gradient-to-br from-[#4A148C]/5 to-[#7B1FA2]/5 border border-[#B497D6]/30 rounded-xl backdrop-blur-sm">
                    <h3 className="font-semibold mb-3 flex items-center gap-2 text-[#4A148C]">
                      <InfoIcon className="h-4 w-4" />
                      ENS Information
                    </h3>
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between p-2 bg-white/60 rounded-lg">
                        <span className="text-gray-600 font-medium">Owner:</span>
                        <span className="font-mono text-[#4A148C] font-semibold">{shortenAddress(currentENSData.owner!)}</span>
                      </div>
                      <div className="flex justify-between p-2 bg-white/60 rounded-lg">
                        <span className="text-gray-600 font-medium">Owned by you:</span>
                        <Badge className={currentENSData.isOwned ? "bg-green-100 text-green-800 border border-green-200" : "bg-red-100 text-red-800 border border-red-200"}>
                          {currentENSData.isOwned ? "Yes" : "No"}
                        </Badge>
                      </div>
                      {currentENSData.isOwned && (
                        <div className="flex justify-between p-2 bg-white/60 rounded-lg">
                          <span className="text-gray-600 font-medium">NFT Status:</span>
                          <Badge className="bg-blue-100 text-blue-800 border border-blue-200">
                            <ImageIcon className="mr-1 h-3 w-3" />
                            Added to Wallet
                          </Badge>
                        </div>
                      )}
                      {currentENSData.avatar && (
                        <div className="flex justify-between p-2 bg-white/60 rounded-lg">
                          <span className="text-gray-600 font-medium">Avatar:</span>
                          <span className="text-blue-600 font-semibold">Available</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </Card>

            {/* Transfer Ownership - Collapsible */}
            <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
              <div className="p-6">
                <Button
                  variant="outline"
                  onClick={() => setShowTransferComponent(!showTransferComponent)}
                  className="w-full flex items-center justify-between border-[#7B1FA2]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 hover:text-[#4A148C] transition-all duration-300"
                >
                  <div className="flex items-center gap-2">
                    <ArrowRightIcon className="h-5 w-5 text-[#4A148C]" />
                    <span className="text-lg font-semibold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">Transfer ENS Ownership</span>
                  </div>
                  <div className={`transform transition-transform ${showTransferComponent ? 'rotate-90' : ''}`}>
                    <ArrowRightIcon className="h-4 w-4 text-[#4A148C]" />
                  </div>
                </Button>
              </div>

              {showTransferComponent && (
                <div className="px-6 pb-6 space-y-6 border-t border-gray-200">
                  <div className="pt-6">
                    <div className="p-4 bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-xl backdrop-blur-sm mb-6">
                      <p className="text-sm text-yellow-800 font-medium">
                        <strong>Warning:</strong> Transferring ENS ownership is permanent.
                        Make sure you trust the recipient address.
                      </p>
                    </div>

                    <div className="space-y-4">
                      <div className="space-y-3">
                        <Label htmlFor="transferEns">ENS Name to Transfer</Label>
                        <Input
                          id="transferEns"
                          type="text"
                          placeholder="yourname.eth"
                          value={ensName}
                          onChange={(e) => setEnsName(e.target.value.toLowerCase())}
                          disabled={!isConnected}
                        />
                      </div>

                      <div className="space-y-3">
                        <Label htmlFor="newOwner">New Owner Address</Label>
                        <Input
                          id="newOwner"
                          type="text"
                          placeholder="0x..."
                          value={newOwnerAddress}
                          onChange={(e) => setNewOwnerAddress(e.target.value)}
                          disabled={!isConnected}
                        />
                      </div>

                      <Button
                        onClick={handleTransferOwnership}
                        disabled={!isConnected || !ensName || !newOwnerAddress || isTransferring || chainId !== mainnet.id}
                        variant="destructive"
                        className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white hover:shadow-lg hover:shadow-red-500/25 hover:scale-105 transition-all duration-300"
                      >
                        {isTransferring ? (
                          <>
                            <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                            Transferring...
                          </>
                        ) : (
                          <>
                            <ArrowRightIcon className="mr-2 h-4 w-4" />
                            Transfer Ownership
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </Card>
          </div>

          {/* Connected ENS Names */}
          <div className="mt-8">
            <h2 className="text-2xl font-semibold mb-6 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">Connected ENS Names</h2>

            {ensConnections.length === 0 ? (
              <Card className="p-8 text-center bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                <div className="text-gray-500">
                  <WalletIcon className="h-12 w-12 mx-auto mb-4 opacity-50 text-[#4A148C]" />
                  <p className="text-lg font-medium mb-2 text-[#4A148C]">No ENS names connected</p>
                  <p className="text-sm">Connect your first ENS name to get started</p>
                </div>
              </Card>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {ensConnections.map((connection, index) => (
                  <Card key={index} className="p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl hover:scale-[1.02] transition-all duration-300">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-lg text-[#4A148C]">
                          {connection.ensName}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {getProjectName(connection.projectId)}
                        </p>
                      </div>
                      {connection.avatar && (
                        <div className="w-10 h-10 bg-gradient-to-br from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center text-white font-semibold">
                          NFT
                        </div>
                      )}
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Owner:</span>
                        <span className="font-mono">{shortenAddress(connection.owner)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Connected:</span>
                        <span>{new Date(connection.connectedAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <Badge className="bg-green-100 text-green-800">
                        <CheckCircleIcon className="mr-1 h-3 w-3" />
                        Active
                      </Badge>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Custom Animations */}
    </DashboardLayoutWrapper>
  );
}
