"use client"

import React, { useState, useEffect, useCallback } from "react"
import { DashboardSidebar } from "@/components/shared/dashboard/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Modal } from "@/components/shared/modal"
import { CopyIcon, RefreshCcwIcon, EyeIcon, EyeOffIcon, AlertCircle } from "lucide-react"
import { ProfileDropdown } from "@/components/shared/profile-dropdown"
import { useAuth } from "@/lib/auth-context"
import { apiService, Application } from "@/lib/api"
import { toast } from "sonner"

export default function ApiKeysPage() {
  const { token } = useAuth()
  const [applications, setApplications] = useState<Application[]>([])
  const [visibleKeys, setVisibleKeys] = useState<Record<string, boolean>>({})
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [newKey, setNewKey] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch applications from the API
  const fetchApplications = useCallback(async () => {
    if (!token) return
    
    try {
      setIsLoading(true)
      setError(null)
      const response = await apiService.getApplications(token)
      
      if (response.success && response.data) {
        setApplications(response.data)
        
        // Initialize visibility state for all keys
        const initialVisibility: Record<string, boolean> = {}
        response.data.forEach(app => {
          if (app.apiKey) {
            initialVisibility[app.appId] = false
          }
        })
        setVisibleKeys(initialVisibility)
      } else {
        setError(response.error || "Failed to fetch applications")
        toast.error(response.error || "Failed to fetch applications")
      }
    } catch (error) {
      setError("Failed to fetch applications. Please try again.")
      toast.error("Failed to fetch applications")
      console.error('Error fetching applications:', error)
    } finally {
      setIsLoading(false)
    }
  }, [token])

  useEffect(() => {
    fetchApplications()
  }, [fetchApplications])

  const handleGenerateKey = () => {
    setIsLoading(true)
    // Mock API call for generating a new key
    setTimeout(() => {
      const generatedKey = `sk_new_${Math.random().toString(36).substr(2, 10)}`
      setNewKey(generatedKey)
      setIsLoading(false)
    }, 1000)
  }

  // Toggle API key visibility
  const toggleKeyVisibility = (appId: string) => {
    setVisibleKeys(prev => ({
      ...prev,
      [appId]: !prev[appId]
    }))
  }

  const handleCopyKey = (key: string) => {
    navigator.clipboard.writeText(key)
    toast.success("API key copied to clipboard!")
  }
  
  // Format date to be more readable
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric'
    }).format(date)
  }

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute w-64 h-64 bg-[#7B1FA2]/10 rounded-full blur-3xl animate-pulse" style={{ top: "10%", left: "5%" }}></div>
      <div className="absolute w-80 h-80 bg-[#4A148C]/5 rounded-full blur-3xl animate-float" style={{ top: "60%", right: "10%" }}></div>
      <div className="absolute w-48 h-48 bg-[#7B1FA2]/5 rounded-full blur-3xl animate-bounce-slow" style={{ bottom: "20%", left: "20%" }}></div>

      <div className="flex min-h-screen relative z-10">
        <DashboardSidebar />
        
        <main className="flex-1 p-6 lg:p-8">
          <header className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
              API Keys
            </h1>
            <div className="flex items-center gap-4">
              <Button 
                onClick={() => setIsModalOpen(true)}
                className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-lg"
              >
                {applications.length === 0 ? "Create Application" : "Generate API Key"}
              </Button>
              <ProfileDropdown />
            </div>
          </header>

          {isLoading ? (
            <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl p-12 flex flex-col items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4B0082] mb-4"></div>
              <p className="text-gray-600">Loading applications and API keys...</p>
            </div>
          ) : error ? (
            <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl p-12 flex flex-col items-center justify-center">
              <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
              <p className="text-gray-800 font-semibold">{error}</p>
              <Button 
                onClick={fetchApplications}
                className="mt-4 bg-[#4B0082] text-white hover:bg-opacity-90"
              >
                Try Again
              </Button>
            </div>
          ) : applications.length === 0 ? (
            <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl p-12 flex flex-col items-center justify-center">
              <p className="text-gray-800">No applications found.</p>
              <p className="text-gray-600 mt-1">Create an application first to generate API keys.</p>
              <Button 
                onClick={() => window.location.href = '/dashboard/dashboard/applications'}
                className="mt-4 bg-[#4B0082] text-white hover:bg-opacity-90"
              >
                Create Application
              </Button>
            </div>
          ) : (
            <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50/80">
                  <tr>
                    <th className="px-6 py-4 text-left font-semibold text-gray-900">Application Name</th>
                    <th className="px-6 py-4 text-left font-semibold text-gray-900">API Key</th>
                    <th className="px-6 py-4 text-left font-semibold text-gray-900">Created On</th>
                    <th className="px-6 py-4 text-left font-semibold text-gray-900">Allowed Domains</th>
                    <th className="px-6 py-4 text-right font-semibold text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {applications.map((app) => (
                    <tr key={app.appId} className="border-b border-gray-200 hover:bg-gray-50/50 transition-colors duration-200">
                      <td className="px-6 py-4 text-gray-900">{app.name}</td>
                      <td className="px-6 py-4 font-mono text-sm text-gray-700">
                        {app.apiKey ? (
                          visibleKeys[app.appId] ? (
                            app.apiKey
                          ) : (
                            "••••••••••••••••••••••••••••••"
                          )
                        ) : (
                          <span className="text-gray-400 italic">No API key generated</span>
                        )}
                      </td>
                      <td className="px-6 py-4 text-gray-600">{formatDate(app.createdAt)}</td>
                      <td className="px-6 py-4 text-gray-600">
                        {app.allowedDomains && app.allowedDomains.length > 0
                          ? app.allowedDomains.join(", ")
                          : <span className="text-gray-400 italic">None specified</span>
                        }
                      </td>
                      <td className="px-6 py-4 text-right">
                        {app.apiKey && (
                          <>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => toggleKeyVisibility(app.appId)}
                              className="hover:bg-[#7B1FA2]/10 hover:text-[#4A148C] transition-all duration-300"
                              title={visibleKeys[app.appId] ? "Hide API Key" : "Show API Key"}
                            >
                              {visibleKeys[app.appId] ? (
                                <EyeOffIcon className="h-4 w-4" />
                              ) : (
                                <EyeIcon className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => app.apiKey && handleCopyKey(app.apiKey)}
                              className="hover:bg-[#7B1FA2]/10 hover:text-[#4A148C] transition-all duration-300"
                              title="Copy API Key"
                            >
                              <CopyIcon className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => window.location.href = `/dashboard/dashboard/applications?id=${app.appId}`}
                          className="hover:bg-[#7B1FA2]/10 hover:text-[#4A148C] transition-all duration-300"
                          title="Manage Application"
                        >
                          <RefreshCcwIcon className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          <Modal
            open={isModalOpen}
            onOpenChange={(open: boolean) => {
              setIsModalOpen(open);
              if (!open) {
                setNewKey("");
              }
            }}
            title={newKey ? "API Key Generated" : "Generate New API Key"}
            actionLabel={newKey ? "Copy Key" : "Generate"}
            onAction={newKey ? () => handleCopyKey(newKey) : handleGenerateKey}
            isLoading={isLoading}
          >
            {newKey ? (
              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  This is your new API key. Make sure to save it securely as you won't be able to see it again.
                </p>
                <div className="bg-gray-50 p-3 rounded-lg font-mono text-sm text-gray-800 border border-gray-300 break-all">
                  {newKey}
                </div>
                <p className="text-xs text-amber-600">
                  <AlertCircle className="inline h-3 w-3 mr-1" />
                  Copy this key now. For security reasons, it will not be displayed again.
                </p>
              </div>
            ) : applications.length === 0 ? (
              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  You need to create an application before you can generate API keys.
                </p>
                <Button 
                  onClick={() => window.location.href = '/dashboard/dashboard/applications'}
                  className="w-full bg-[#4B0082] text-white hover:bg-opacity-90"
                >
                  Create Application
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  Select an application to generate or regenerate its API key.
                </p>
                <div className="grid gap-2">
                  {applications.map((app) => (
                    <Button 
                      key={app.appId} 
                      variant="outline"
                      className="justify-start text-left w-full hover:bg-[#B497D6]/10 hover:text-[#4B0082] transition-all duration-300"
                      onClick={() => {
                        // In a real implementation, you'd call an API to generate a key for this app
                        setIsLoading(true);
                        // Mock API call
                        setTimeout(() => {
                          const generatedKey = `sk_${app.name.toLowerCase().replace(/\s+/g, '_')}_${Math.random().toString(36).substr(2, 10)}`;
                          setNewKey(generatedKey);
                          setIsLoading(false);
                        }, 1000);
                      }}
                    >
                      {app.name} {app.apiKey ? "(Regenerate)" : "(Generate)"}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </Modal>
        </main>
      </div>
    </div>
  )
}