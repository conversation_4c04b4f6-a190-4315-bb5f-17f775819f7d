"use client"

import React from "react"
import { DashboardSidebar } from "@/components/shared/dashboard/sidebar"
import { ProfileDropdown } from "@/components/shared/profile-dropdown"

interface DashboardLayoutProps {
  children: React.ReactNode
  title: string
  actions?: React.ReactNode
}

export function DashboardLayoutWrapper({ children, title, actions }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute top-[10%] left-[5%] w-64 h-64 bg-[#7B1FA2]/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute top-[60%] right-[10%] w-80 h-80 bg-[#4A148C]/5 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-[20%] left-[20%] w-48 h-48 bg-[#7B1FA2]/5 rounded-full blur-3xl animate-bounce-slow"></div>

      <div className="flex min-h-screen relative z-10">
        <DashboardSidebar />
        
        <main className="flex-1 p-4 sm:p-6 lg:p-8">
          {/* Mobile Header */}
          <div className="lg:hidden flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                {title}
              </h1>
            </div>
            <ProfileDropdown />
          </div>

          {/* Desktop Header */}
          <header className="hidden lg:flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
              {title}
            </h1>
            <div className="flex items-center gap-4">
              {actions}
              <ProfileDropdown />
            </div>
          </header>

          {/* Mobile Actions */}
          {actions && (
            <div className="lg:hidden mb-6">
              {actions}
            </div>
          )}

          {children}
        </main>
      </div>
    </div>
  )
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
